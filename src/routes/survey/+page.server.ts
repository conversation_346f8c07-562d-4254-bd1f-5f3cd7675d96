import { error } from '@sveltejs/kit';
import { FORMBRICKS_API_HOST, FORMBRICKS_API_KEY } from '$env/static/private';
import type { PageServerLoad } from "./$types";
import type { FormBricksSurvey } from '$lib/types.js';
 //http://localhost:5173/survey?id=cmcatfekkbn4iwc01zy32jopv

export const load: PageServerLoad = async ({ url }: { url: URL }) => {
	// Get the survey ID from query parameters
	const surveyId = url.searchParams.get('id');

	if (!surveyId) {
		throw error(400, 'Survey ID is required');
	}

	// Validate environment variables
	if (!FORMBRICKS_API_HOST || !FORMBRICKS_API_KEY) {
		throw error(500, 'FormBricks configuration is missing');
	}

	try {
		// Fetch survey from FormBricks API
		const response = await fetch(`${FORMBRICKS_API_HOST}/api/v1/management/surveys/${surveyId}`, {
			method: 'GET',
			headers: {
				//'Authorization': `Bearer ${FORMBRICKS_API_KEY}`,
				'x-api-key': FORMBRICKS_API_KEY,
				'Accept': 'application/json',
                'Content-Type': 'application/json'
			}
		});

		if (!response.ok) {
			if (response.status === 404) {
				throw error(404, 'Survey not found');
			}
			if (response.status === 401) {
				throw error(401, 'Invalid API key');
			}
			throw error(response.status, `Failed to fetch survey: ${response.statusText}`);
		}

        const res_json = await response.json();

     	const survey: FormBricksSurvey =res_json.data;

		return {
			survey
		};
	} catch (err) {
		// Handle network errors and other exceptions
		if (err instanceof Error && 'status' in err) {
			// Re-throw SvelteKit errors
			throw err;
		}

		console.error('Error fetching survey:', err);
		throw error(500, 'Failed to load survey');
	}
};