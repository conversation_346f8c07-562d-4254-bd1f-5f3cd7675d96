<script lang="ts">
    import VoiceChat from '$lib/voicechat/voicechat.svelte';
	import type { <PERSON><PERSON>ricksSurvey } from '$lib/types.js';
    import type { ChatMessage } from '$lib/types';

	

    type SurveyData = {
		survey: FormBricksSurvey;
	};
    const { data } = $props<{
		data: SurveyData;
	}>();


    let messages = $state<ChatMessage[]>([]);

    // svelte-ignore non_reactive_update
        let prompt = `You are a survey agent. The user and you will engage in a spoken 
  conversation where you will ask the user a series of questions to gather information.
  Keep your responses short, generally two or three sentences for chatty scenarios.
  When the user is ready to begin, you will introduce yourself and ask the first question.
  Your job is to ask questions to gather the following information from the user:
  <questions> `;

    for (let i = 0; i < data.survey.questions.length; i++) {
        prompt += data.survey.questions[i].headline.default + '\n';
    }
    prompt += '</questions>';

    prompt += `
    You have only 7 mins to complete this task.

    
    `;

    console.log(JSON.stringify(data.survey.questions  ));
</script>


<VoiceChat bind:messages={messages}    system_prompt={prompt} />