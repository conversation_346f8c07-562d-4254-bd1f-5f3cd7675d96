<script lang="ts">
	import Button from '$lib/components/ui/button/button.svelte';
	import { Volume2, VolumeX, Mic, MicOff } from '@lucide/svelte';
	import { Slider } from 'bits-ui';

	interface Props {
		type: 'input' | 'output';
		value: number;
		muted?: boolean;
		onValueChange?: (value: number) => void;
		onMuteToggle?: (muted: boolean) => void;
		min?: number;
		max?: number;
		step?: number;
	}

	let {
		type,
		value = 1.0,
		muted = false,
		onValueChange,
		onMuteToggle,
		min = 0,
		max = 2,
		step = 0.01
	}: Props = $props();

	function toggleMute() {
		const newMuted = !muted;
		onMuteToggle?.(newMuted);
	}

	// Convert gain to percentage for display
	let percentage = $derived(Math.round((value / max) * 100));

	// Convert gain to dB for display
	let dB = $derived(value > 0 ? Math.round(20 * Math.log10(value)) : -Infinity);
</script>

<div class="flex flex-col gap-3">
	<div class="flex items-center justify-between"></div>

	<div class="flex h-[240px] w-full justify-center">
		<Slider.Root
			type="single"
			bind:value
			{min}
			{max}
			{step}
			onValueCommit={(v) => {
				onValueChange?.(v);
			}}
			orientation="vertical"
			class="relative flex h-full touch-none flex-col items-center select-none"
			trackPadding={3}
		>
			<span class="relative h-full w-2 cursor-pointer overflow-hidden rounded-full bg-gray-200">
				<Slider.Range
					class={value > 1.7
						? 'absolute w-full bg-red-500'
						: value > 1.4
							? 'absolute w-full bg-yellow-500'
							: 'bg-foreground absolute w-full'}
				/>
			</span>
			<Slider.Thumb
				index={0}
				class="border-border-input bg-background hover:border-dark-40 
			focus-visible:ring-foreground dark:bg-foreground dark:shadow-card data-active:border-dark-40 z-5 
			block size-[25px] cursor-pointer rounded-full border 
			text-center shadow-sm transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 
			focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50 data-active:scale-[0.98] 
			{value > 1.7 ? ' text-red-400' : value > 1.4 ? ' text-yellow-400' : ''}"
			>
				{#if value > 1.7}
					🚨
				{:else if value > 1.4}
					⚠️
				{:else}
					&nbsp;
				{/if}
			</Slider.Thumb>
		</Slider.Root>
	</div>
	<div class="flex justify-center">
		<Button onclick={toggleMute} title={muted ? 'Unmute' : 'Mute'}>
			{#if type === 'input'}
				{#if muted}
					<MicOff class="inline h-4 w-4" />Unmute
				{:else}
					<Mic class="inline h-4 w-4" />Mute
				{/if}
			{:else if muted}
				<VolumeX class="h-4 w-4" />
			{:else}
				<Volume2 class="h-4 w-4" />
			{/if}
		</Button>
	</div>
</div>
