<script lang="ts">
	import { onMount } from 'svelte';
	import { Play, Square, Settings, Activity } from '@lucide/svelte';
	import AudioMeter from './meter.svelte';
	import DeviceSelector from './deviceselect.svelte';
	import VolumeControl from './volume.svelte';
	import SpectrumAnalyzer from '$lib/components/SpectrumAnalyzer.svelte';
	//import { audioManager } from '$lib/audioManager';
	import type { AudioSettings } from './types';
	import { AudioManager } from './audiomanager';

	let isRecording = $state(false);
	let settings = $state<AudioSettings>({
		inputDeviceId: 'default',
		outputDeviceId: 'default',
		inputGain: 1.0,
		outputGain: 1.0,
		muted: false,
		sampleRate: 48000,
		bufferSize: 256
	});

	let showSettings = $state(false);
	let permissionsGranted = $state(false);
	let errorMessage = $state<string | null>(null);


	let { audioManager } = $props<{ audioManager: AudioManager }>();		




	onMount(async () => {
		// Request permissions and initialize
		try {
			permissionsGranted = await audioManager.requestPermissions();
			if (!permissionsGranted) {
				errorMessage = 'Microphone permissions are required for audio monitoring.';
			}
			settings = audioManager.getSettings();
		} catch (error) {
			console.error('Failed to initialize audio:', error);
			errorMessage = 'Failed to initialize audio system.';
		}
	});

	$effect(() => {
		return () => {
			if (isRecording) {
				stopAudio();
			}
		};
	});

	async function startAudio() {
		try {
			errorMessage = null;
			const success = await audioManager.startAudioInput(settings.inputDeviceId);
			if (success) {
				isRecording = true;
			} else {
				errorMessage = 'Failed to start audio input.';
			}
		} catch (error) {
			console.error('Failed to start audio:', error);
			errorMessage = 'Failed to start audio input.';
		}
	}

	function stopAudio() {
		audioManager.stopAudioInput();
		isRecording = false;
	}

	function handleInputDeviceChange(deviceId: string) :void {
		settings.inputDeviceId = deviceId;
		if (isRecording) {
			// Restart with new device
			stopAudio();
			startAudio();
		}
		
	}

	function handleOutputDeviceChange(deviceId: string):void {
		settings.outputDeviceId = deviceId;
		
	}

	function handleInputGainChange(gain: number) {
		settings.inputGain = gain;
		audioManager.setInputGain(gain);
	}

	function handleOutputGainChange(gain: number) {
		settings.outputGain = gain;
		audioManager.setOutputGain(gain);
	}

	function handleMuteToggle(muted: boolean) {
		settings.muted = muted;
		audioManager.setMuted(muted);
	}

	async function requestPermissions() {
		try {
			permissionsGranted = await audioManager.requestPermissions();
			if (permissionsGranted) {
				errorMessage = null;
			}
		} catch (error) {
			errorMessage = 'Failed to request microphone permissions.';
		}
	}
</script>

<main class="bg-audio-bg dark:bg-audio-bg min-h-screen p-4 transition-colors">
	<div class="mx-auto max-w-7xl">
		<!-- Header -->
		<header class="mb-8 flex items-center justify-between">
			<div class="flex items-center gap-4">
				{#if permissionsGranted}
					<button
						type="button"
						onclick={isRecording ? stopAudio : startAudio}
						class="flex items-center gap-2 rounded-lg px-4 py-2 font-medium transition-colors {isRecording
							? 'bg-red-600 text-white hover:bg-red-700'
							: 'bg-audio-accent dark:bg-audio-accent hover:bg-audio-accent/80 dark:hover:bg-audio-accent/80 text-black'}"
					>
						{#if isRecording}
							<Square class="h-4 w-4" />
							Stop Monitoring
						{:else}
							<Play class="h-4 w-4" />
							Start Monitoring
						{/if}
					</button>
				{:else}
					<button
						type="button"
						onclick={requestPermissions}
						class="bg-audio-accent dark:bg-audio-accent hover:bg-audio-accent/80 dark:hover:bg-audio-accent/80 rounded-lg
						       px-4 py-2
						       font-medium text-black transition-colors"
					>
						Grant Permissions
					</button>
				{/if}
			</div>
			<div class="flex items-center gap-4 text-sm">
				<div>
					<span class="text-audio-muted dark:text-audio-muted">Status:</span>
					<span
						class="text-audio-text dark:text-audio-text ml-2 {isRecording
							? 'text-green-400 dark:text-green-600'
							: 'text-audio-muted dark:text-audio-muted'}"
					>
						{isRecording ? 'Active' : 'Inactive'}
					</span>
				</div>
				<div>
					<span class="text-audio-muted dark:text-audio-muted">Permissions:</span>
					<span
						class="text-audio-text dark:text-audio-text ml-2 {permissionsGranted
							? 'text-green-400 dark:text-green-600'
							: 'text-red-400 dark:text-red-600'}"
					>
						{permissionsGranted ? 'Granted' : 'Denied'}
					</span>
				</div>
			</div>
		</header>

		<!-- Error Message -->
		{#if errorMessage}
			<div
				class="mb-6 rounded-lg border border-red-500/30
			            bg-red-900/20 p-4 text-red-400
			            dark:border-red-300 dark:bg-red-100 dark:text-red-700"
			>
				{errorMessage}
			</div>
		{/if}

		<!-- Mixer 	meter,gain, device -->
		<div class="">
			<div class="row grid grid-cols-2">
				<div class="justify-center">
					<div class="text-center">
						<h3 class="text-audio-muted dark:text-audio-muted mb-2 text-lg font-medium">Input</h3>
						<AudioMeter onMeterUpdate={audioManager.onMeterUpdate} orientation="vertical" size="medium" showNumbers={false} />
					</div>
				</div>
				<div class="grid grid-cols-2 ">
					<div class="justify-center">
						<h3 class="text-audio-muted dark:text-audio-muted mb-2 text-lg font-medium text-center">L</h3>
						<AudioMeter onMeterUpdate={audioManager.onMeterUpdate} orientation="vertical" size="medium" showNumbers={false} />
					</div>
					<div class="justify-center">
						<h3 class="text-audio-muted dark:text-audio-muted mb-2 text-lg font-medium text-center">R</h3>
						<AudioMeter onMeterUpdate={audioManager.onMeterUpdate} orientation="vertical" size="medium" showNumbers={false} />
					</div>
				</div>
			</div>

			<div class="row grid grid-cols-2">
				<div class="justify-center">
					<VolumeControl
							type="input"
							value={settings.inputGain}
							muted={settings.muted}
							onValueChange={handleInputGainChange}
							onMuteToggle={handleMuteToggle}
						/>
				</div>
				<div class="justify-center">
					<VolumeControl
							type="output"
							value={settings.outputGain}
							onValueChange={handleOutputGainChange}
						/>
				</div>
			</div>

			<div class="row grid grid-cols-2">
				<div class="justify-center ">
					<DeviceSelector
						type="input"
						selectedDeviceId={settings.inputDeviceId}
						handleDeviceChange={handleInputDeviceChange}
						getDevices={audioManager.getInputDevices}
						onDeviceChange={audioManager.onDeviceChange}
					/>
				</div>
				<div class="justify-center">
					<DeviceSelector
						type="output"
						selectedDeviceId={settings.outputDeviceId}
						handleDeviceChange={handleOutputDeviceChange}
						getDevices={audioManager.getOutputDevices}
						onDeviceChange={audioManager.onDeviceChange}
					/>
				</div>
			</div>
		</div>

		

		

		
	</div>
</main>
