class MicrophoneProcessor extends AudioWorkletProcessor {
	constructor() {
		super();
		this.isStreaming = false;
		this.samplingRatio = 1;
		this.isFirefox = false;

		// Listen for configuration updates
		this.port.onmessage = (event) => {
			const { type, data } = event.data;
			if (type === 'updateConfig') {
				this.isStreaming = data.isStreaming;
				this.samplingRatio = data.samplingRatio;
				this.isFirefox = data.isFirefox;
			}
		};
	}

	process(inputs, outputs, parameters) {
		if (!this.isStreaming || !inputs[0] || !inputs[0][0]) {
			return true;
		}

		const inputData = inputs[0][0]; // Get first channel
		const numSamples = Math.round(inputData.length / this.samplingRatio);
		const pcmData = this.isFirefox ? new Int16Array(numSamples) : new Int16Array(inputData.length);

		// Convert to 16-bit PCM
		if (this.isFirefox) {
			for (let i = 0; i < numSamples; i++) {
				// NOTE: for firefox the samplingRatio is not 1,
				// so it will downsample by skipping some input samples
				// A better approach is to compute the mean of the samplingRatio samples.
				// or pass through a low-pass filter first
				// But skipping is a preferable low-latency operation
				const sampleIndex = Math.floor(i * this.samplingRatio);
				if (sampleIndex < inputData.length) {
					pcmData[i] = Math.max(-1, Math.min(1, inputData[sampleIndex])) * 0x7fff;
				}
			}
		} else {
			for (let i = 0; i < inputData.length; i++) {
				pcmData[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7fff;
			}
		}

		// Send PCM data to main thread
		this.port.postMessage({
			type: 'audioData',
			data: pcmData.buffer
		});

		return true;
	}
}

registerProcessor('microphone-processor', MicrophoneProcessor);
