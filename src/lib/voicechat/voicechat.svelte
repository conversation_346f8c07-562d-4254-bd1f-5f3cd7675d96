<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { Toaster } from '$lib/components/ui/sonner';
	import Button, { buttonVariants } from '$lib/components/ui/button/button.svelte';
	import * as Sheet from '$lib/components/ui/sheet/index.js';
	import {Activity, Settings } from '@lucide/svelte';

	import ChatDisplay from './chatdisplay.svelte';
	import type { ChatMessage } from '$lib/types';
	import { AudioPlayer } from './audio-player';
	import { onMount } from 'svelte';
	import { arrayBufferToBase64, base64ToFloat32Array } from './util';
	import { io, Socket } from 'socket.io-client';
	import { PUBLIC_S2S_SERVER } from '$env/static/public';
	import Dashboard from './dashboard.svelte';
	import { AudioManager } from './audiomanager';

	const MAGIC_END_CONVERSATION = 'Thank you for your time'; // TODO: add to config and auto add to aisurvey prompt

	// Component props
	let { system_prompt, messages = $bindable() } = $props<{
		system_prompt: string;
		messages: ChatMessage[];
	}>();

	// state
	let thinking = $state(false);
	let listening = $state(false);
	let isStreaming = $state(false);
	let startButtonEnabled = $state(false);
	let socket: Socket;

	const audioManager: AudioManager = new AudioManager();

	
	let toastId = $state<any>(); // status

	function setStatus(msg: string, level: string, dur: any) {
		if (toastId) {
			toast.dismiss(toastId);
		}
		//toastId = toast[level](msg, { duration: dur ? dur: Infinity });
		//toast.call()
		if (level == 'error') {
			toastId = toast.error(msg, { duration: dur ? dur : Infinity });
		} else if (level == 'success') {
			toastId = toast.success(msg, { duration: dur ? dur : Infinity });
		} else if (level == 'warning') {
			toastId = toast.warning(msg, { duration: dur ? dur : Infinity });
		} else {
			toastId = toast.info(msg, { duration: dur ? dur : Infinity });
		}
	}

	onMount(() => {
		// Connect to the server
		socket = io(PUBLIC_S2S_SERVER);
		//audioManager = new AudioManager();
		InitEventHandlers();
		
		audioManager.InitInput(sendAudioInput);
		audioManager.InitOutput();
		audioManager.startInput();
		//audioManager.startOutput();
		//initAudio();
		const r = socket.connect();

		// Optional return function
		return () => {
			// This is executed when the component is removed from the DOM
			socket.disconnect();
		};
	});

	function toggleStreaming() {
		if (isStreaming) {
			stopStreaming();
		} else {
			startStreaming();
		}
	}

	function handleInputGainChange(gain: number) {
		audioManager?.setInputGain(gain);
		
	}



	function addTextMessage(content: { role: any; message: string }) {
		let updatedChatHistory = [...messages];
		let lastTurn = updatedChatHistory[updatedChatHistory.length - 1];

		if (lastTurn !== undefined && lastTurn.role === content.role) {
			// Same role, append to the last turn
			updatedChatHistory[updatedChatHistory.length - 1] = {
				...content,
				message: lastTurn.message + ' ' + content.message
			};
		} else {
			// Different role, add a new turn
			updatedChatHistory.push({
				role: content.role,
				message: content.message
			});
		}
		messages = updatedChatHistory;
	}

	function endTurn() {
		let updatedChatHistory = messages.map((item: ChatMessage) => {
			return {
				...item,
				endOfResponse: true
			};
		});

		messages = updatedChatHistory;
	}

	function endConversation() {
		let updatedChatHistory = messages.map((item: ChatMessage) => {
			return {
				...item,
				endOfResponse: true
			};
		});

		updatedChatHistory.push({
			role: '',
			message: '',
			endOfResponse: true,
			endOfConversation: true
		});

		messages = updatedChatHistory;
	}

	let transcriptionReceived = false;
	let displayAssistantText = false;
	let role: string;
	let sessionInitialized = false;

	// Custom system prompt - you can modify this
	/*let SYSTEM_PROMPT =
		'You are a friend. The user and you will engage in a spoken ' +
		'dialog exchanging the transcripts of a natural real-time conversation. Keep your responses short, ' +
		'generally two or three sentences for chatty scenarios.';
	*/
	const SYSTEM_PROMPT = `You are a survey agent. The user and you will engage in a spoken 
  conversation where you will ask the user a series of questions to gather information.
  Keep your responses short, generally two or three sentences for chatty scenarios.
  When the user is ready to begin, you will introduce yourself and ask the first question.
  Use the tool to get the questions to ask the user.  Do not ask any questions that are not provided by the tool.
  Always start by using getNextQuestionTool to get the first question.
  `;


	
	// Initialize the session with Bedrock
	async function initializeSession() {
		if (sessionInitialized) return;

		setStatus('Initializing session...', 'info', Infinity);

		try {
			// Send events in sequence
			socket.emit('promptStart');
			socket.emit('systemPrompt', system_prompt || SYSTEM_PROMPT);
			socket.emit('audioStart');

			// Mark session as initialized
			sessionInitialized = true;
			setStatus('Session initialized successfully', 'success', 10000);
		} catch (error: any) {
			console.error('Failed to initialize session:', error);
			setStatus('Error: ' + error.message, 'error', Infinity);
		}
	}

	async function startStreaming() {
		if (isStreaming) return;

		try {
			// First, make sure the session is initialized
			if (!sessionInitialized) {
				await initializeSession();
			}

			isStreaming = true;
			setStatus('Listening... Speak now', 'success', 20000);

			// Show user thinking/listening indicator when starting to record
			transcriptionReceived = false;
			listening = true;
		} catch (error: any) {
			console.error('Error starting recording:', error);
			setStatus('Error: ' + error.message, 'error', Infinity);

			// Fallback to legacy implementation if AudioWorklet fails
			console.warn('AudioWorklet failed, falling back to ScriptProcessorNode');
			//await startStreamingLegacy();
		}
	}

	

	function stopStreaming() {
		if (!isStreaming) return;

		isStreaming = false;

		

		setStatus('Processing...', 'warning', 10000);

		
		//audioManager.stopInput();
		audioManager.stopOutput();

		// Tell server to finalize processing
		socket.emit('stopAudio');

		// End the current turn in chat history
		endTurn();
	}


	const sendAudioInput = (base64Data: string) => {
		socket.emit('audioInput', base64Data);
	}




	// Process message data and add to chat history
	function handleTextOutput(data: { role: any; content: any }) {
		console.log('Processing text output:', data);
		if (data.content) {
			const messageData = {
				role: data.role,
				message: data.content
			};
			addTextMessage(messageData);
			if (messageData.message.toLowerCase().includes(MAGIC_END_CONVERSATION.toLowerCase())) {
				stopStreaming();
			}
		}
	}

	// EVENT HANDLERS
	// --------------
	function InitEventHandlers() {
		// Handle content start from the server
		socket.on('contentStart', (data) => {
			console.log('Content start received:', data);

			if (data.type === 'TEXT') {
				// Below update will be enabled when role is moved to the contentStart
				role = data.role;
				if (data.role === 'USER') {
					// When user's text content starts, hide user thinking indicator
					listening = false;
				} else if (data.role === 'ASSISTANT') {
					// When assistant's text content starts, hide assistant thinking indicator
					thinking = false;
					let isSpeculative = false;
					try {
						if (data.additionalModelFields) {
							const additionalFields = JSON.parse(data.additionalModelFields);
							isSpeculative = additionalFields.generationStage === 'SPECULATIVE';
							if (isSpeculative) {
								console.log('Received speculative content');
								displayAssistantText = true;
							} else {
								displayAssistantText = false;
							}
						}
					} catch (e) {
						console.error('Error parsing additionalModelFields:', e);
					}
				}
			} else if (data.type === 'AUDIO') {
				// When audio content starts, we may need to show user thinking/listening indicator
				if (isStreaming) {
					listening = true;
				}
			}
		});

		// Handle text output from the server
		socket.on('textOutput', (data) => {
			console.log('Received text output:', data);

			if (role === 'USER') {
				// When user text is received, show thinking indicator for assistant response
				transcriptionReceived = true;
				//listening = false();

				// Add user message to chat
				handleTextOutput({
					role: data.role,
					content: data.content
				});

				// Show assistant thinking indicator after user text appears
				thinking = true;
			} else if (role === 'ASSISTANT') {
				//thinkng = false();
				if (displayAssistantText) {
					handleTextOutput({
						role: data.role,
						content: data.content
					});
				}
			}
		});

		// Handle audio output
		socket.on('audioOutput', (data) => {
			if (data.content) {
				try {
					const audioData = base64ToFloat32Array(data.content);
					audioManager.playAudio(audioData);
				} catch (error) {
					console.error('Error processing audio data:', error);
				}
			}
		});

		// Handle content end events
		socket.on('contentEnd', (data) => {
			console.log('Content end received:', data);

			if (data.type === 'TEXT') {
				if (role === 'USER') {
					// When user's text content ends, make sure assistant thinking is shown
					listening = false;
					thinking = true;
				} else if (role === 'ASSISTANT') {
					// When assistant's text content ends, prepare for user input in next turn
					thinking = false;
				}

				// Handle stop reasons
				if (data.stopReason && data.stopReason.toUpperCase() === 'END_TURN') {
					endTurn();
				} else if (data.stopReason && data.stopReason.toUpperCase() === 'INTERRUPTED') {
					console.log('Interrupted by user');
					audioManager.bargeIn();
				}
			} else if (data.type === 'AUDIO') {
				// When audio content ends, we may need to show user thinking/listening indicator
				if (isStreaming) {
					listening = true;
				}
			}
		});

		// Stream completion event
		socket.on('streamComplete', () => {
			if (isStreaming) {
				stopStreaming();
			}
			setStatus('Ready', 'info', 10000);
		});

		// Handle connection status updates
		socket.on('connect', () => {
			setStatus('Connected', 'success', 10000);

			sessionInitialized = false;
		});

		socket.on('disconnect', () => {
			setStatus('Disconnected', 'warning', 10000);
			sessionInitialized = false;
			listening = false;
			thinking = false;
		});

		// Handle errors
		socket.on('error', (error) => {
			console.error('Server error:', error);
			setStatus('Error: ' + error.message, 'error', Infinity);
			listening = false;
			thinking = false;
		});
	}
</script>

<div id="voicechat">
	<ChatDisplay bind:messages bind:thinking bind:listening />
	<Button  onclick={toggleStreaming}
		>{isStreaming ? 'Stop' : 'Start'}</Button
	>
	<Toaster richColors expand={false} closeButton position="bottom-right" />

	<Sheet.Root>
		<Sheet.Trigger class={buttonVariants({ variant: 'outline' })}><Settings class="w-6 h-6" /></Sheet.Trigger>
		<Sheet.Content side="right">
			<Sheet.Header>
				<Sheet.Title><Activity class="w-8 h-8 text-audio-accent dark:text-audio-accent inline" /> Settings</Sheet.Title>
				<Sheet.Description>
					
				</Sheet.Description>
			</Sheet.Header>
			
			<Dashboard	audioManager={audioManager} />

			<Sheet.Footer>
				<Sheet.Close class={buttonVariants({ variant: 'outline' })}>Save changes</Sheet.Close>
			</Sheet.Footer>
		</Sheet.Content>
	</Sheet.Root>
</div>
