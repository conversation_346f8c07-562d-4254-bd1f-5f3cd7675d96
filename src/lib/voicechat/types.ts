export interface AudioDevice {
	deviceId: string;
	label: string;
	kind: 'audioinput' | 'audiooutput';
	groupId: string;
}

export interface AudioSettings {
	inputDeviceId: string;
	outputDeviceId: string;
	inputGain: number;
	outputGain: number;
	muted: boolean;
	sampleRate: number;
	bufferSize: number;
}

export interface AudioMeterData {
	level: number;
	peak: number;
	rms: number;
	clipping: boolean;
}
