class AudioMonitorProcessor extends AudioWorkletProcessor {
	process(inputs, outputs, parameters) {
		const input = inputs[0];
		const output = outputs[0];

		if (input.length > 0 && output.length > 0) {
			// Pass audio through unchanged
			for (let channel = 0; channel < input.length; channel++) {
				const inputChannel = input[channel];
				const outputChannel = output[channel];
				outputChannel.set(inputChannel);
			}

			// Send audio data to main thread for listeners
			if (input[0] && input[0].length > 0) {
				this.port.postMessage({
					type: 'audio-played',
					audioData: input[0] // Send first channel
				});
			}
		}

		return true;
	}
}

registerProcessor('audio-monitor-processor', AudioMonitorProcessor);
