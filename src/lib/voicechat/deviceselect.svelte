<script lang="ts">
	import { onMount } from 'svelte';
	import type { AudioDevice } from './types';
	import * as Select from '$lib/components/ui/select/index.js';

	interface Props {
		type: 'input' | 'output';
		selectedDeviceId?: string;
		onDeviceChange: (callback: (devices: AudioDevice[]) => void) =>  (() => void);
		handleDeviceChange: (deviceId: string) => void;
		getDevices: () => AudioDevice[];
	}

	let { type, selectedDeviceId = 'default', onDeviceChange ,handleDeviceChange,getDevices }: Props = $props();

	let deviceId = $state(selectedDeviceId);

	function getValue() {
		return deviceId;
	}

	function setValue(devId: string) {
		deviceId = devId;
		handleDeviceChange(devId);
	}

	let devices = $state<AudioDevice[]>([]);
	let unsubscribe: (() => void) | null = null;

	onMount(async () => {
		// Request permissions first
		// await audioManager.requestPermissions();

		// Get initial devices
		devices = getDevices();
		// type === 'input' ? audioManager.getInputDevices() : audioManager.getOutputDevices();

		// Subscribe to device changes
		unsubscribe = onDeviceChange((allDevices) => {
			devices =
				type === 'input'
					? allDevices.filter((d) => d.kind === 'audioinput')
					: allDevices.filter((d) => d.kind === 'audiooutput');
		});
	});

	$effect(() => {
		return () => {
			if (unsubscribe) {
				unsubscribe();
			}
		};
	});

	let selectedDevice = $derived(
		devices.find((d) => d.deviceId === selectedDeviceId) || {
			deviceId: 'default',
			label: `Default ${type === 'input' ? 'Input' : 'Output'}`,
			kind: type === 'input' ? ('audioinput' as const) : ('audiooutput' as const),
			groupId: ''
		}
	);
</script>

<div class="device-selector relative w-full">

	<Select.Root type="single" name="selectedDeviceId" bind:value={getValue, setValue}>
		<Select.Trigger class="text-xs">{selectedDevice.label.substring(0, 20)}</Select.Trigger>
		<Select.Content>
			{#if devices.length === 0}
				<div class="text-audio-muted dark:text-audio-muted px-3 py-2 text-xs">
					No {type === 'input' ? 'input' : 'output'} devices found
				</div>
			{:else}
				{#each devices as device (device.deviceId)}
					<Select.Item class="text-xs"
					value={device.deviceId}>{device.label}</Select.Item>
				{/each}
			{/if}
		</Select.Content>
	</Select.Root>
</div>
