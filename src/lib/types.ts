// TypeScript interfaces
export interface ChatMessage {
    role: string;
    message: string;
    endOfResponse?: boolean;
    endOfConversation?: boolean;
}

// FormBricks Survey Types
export interface FormBricksSurvey {
    id: string;
    name: string;
    type: 'web' | 'email' | 'link' | 'mobile';
    status: 'draft' | 'inProgress' | 'paused' | 'completed';
    welcomeCard: {
        enabled: boolean;
        headline?: string;
        html?: string;
        timeToFinish?: boolean;
        showResponseCount?: boolean;
    };
    questions: FormBricksQuestion[];
    thankYouCard: {
        enabled: boolean;
        headline?: string;
        subheader?: string;
        buttonLabel?: string;
        buttonLink?: string;
    };
    hiddenFields?: {
        enabled: boolean;
        fieldIds?: string[];
    };
    delay?: number;
    autoClose?: number;
    closeOnDate?: string;
    redirectUrl?: string;
    recontactDays?: number;
    displayLimit?: number;
    autoComplete?: number;
    runOnDate?: string;
    surveyClosedMessage?: {
        enabled: boolean;
        heading?: string;
        subheading?: string;
    };
    singleUse?: {
        enabled: boolean;
        heading?: string;
        subheading?: string;
    };
    verifyEmail?: {
        name?: string;
        subheading?: string;
    };
    productOverwrites?: Record<string, any>;
    styling?: {
        allowStyleOverwrite?: boolean;
        overwriteThemeStyling?: boolean;
        theme?: Record<string, any>;
    };
    segment?: {
        id: string;
        title: string;
        description?: string;
        isPrivate: boolean;
        filters: any[];
    };
    triggers?: Array<{
        actionClass: {
            id: string;
            name: string;
            description?: string;
            type: string;
            key?: string;
            noCodeConfig?: Record<string, any>;
        };
    }>;
    attributeFilters?: Array<{
        attributeClassId: string;
        condition: string;
        value: string;
    }>;
    createdAt: string;
    updatedAt: string;
    environmentId: string;
    createdBy?: string;
    pin?: string;
    resultShareKey?: string;
    languages?: Array<{
        language: {
            id: string;
            code: string;
            alias?: string;
        };
        default: boolean;
    }>;
    showLanguageSwitch?: boolean;
}

export interface FormBricksString {
    default: string;
    translations?: Array<{
        languageId: string;
        value: string;
    }>;
}

export interface FormBricksQuestion {
    id: string;
    type: 'openText' | 'multipleChoiceSingle' | 'multipleChoiceMulti' | 'nps' | 'cta' | 'rating' | 'consent' | 'pictureSelection' | 'cal' | 'fileUpload' | 'matrix' | 'address';
    headline: FormBricksString;
    subheader?: FormBricksString;
    required?: boolean;
    buttonLabel?: FormBricksString;
    backButtonLabel?: FormBricksString;
    scale?: 'number' | 'smiley' | 'star';
    range?: number;
    lowerLabel?: string;
    upperLabel?: string;
    isColorCodingEnabled?: boolean;
    isDraft?: boolean;
    choices?: Array<{
        id: string;
        label: FormBricksString;
        imageUrl?: string;
    }>;
    logic?: Array<{
        condition: string;
        value: string | string[];
        destination: string;
    }>;
    shuffleOption?: 'none' | 'all' | 'exceptLast';
    otherOptionPlaceholder?: string;
    placeholder?: FormBricksString;
    longAnswer?: boolean;
    inputType?: 'text' | 'email' | 'url' | 'number' | 'phone';
    html?: string;
    dismissButtonLabel?: string;
    videoUrl?: string;
    imageUrl?: string;
    allowMultipleFiles?: boolean;
    maxSizeInMB?: number;
    allowedFileExtensions?: string[];
    calUserName?: string;
    rows?: Array<{
        value: string;
    }>;
    columns?: Array<{
        value: string;
    }>;
    addressLine1?: {
        show: boolean;
        required: boolean;
    };
    addressLine2?: {
        show: boolean;
        required: boolean;
    };
    city?: {
        show: boolean;
        required: boolean;
    };
    state?: {
        show: boolean;
        required: boolean;
    };
    zip?: {
        show: boolean;
        required: boolean;
    };
    country?: {
        show: boolean;
        required: boolean;
    };
}

export interface AudioSettings {
	inputDeviceId: string;
	outputDeviceId: string;
	inputGain: number;
	outputGain: number;
	muted: boolean;
	sampleRate: number;
	bufferSize: number;
}