export interface AudioDevice {
	deviceId: string;
	label: string;
	kind: 'audioinput' | 'audiooutput';
	groupId: string;
}

export interface AudioSettings {
	inputDeviceId: string;
	outputDeviceId: string;
	inputGain: number;
	outputGain: number;
	muted: boolean;
	sampleRate: number;
	bufferSize: number;
}

export interface AudioMeterData {
	level: number;
	peak: number;
	rms: number;
	clipping: boolean;
}

export class AudioManager {
	private audioContext: AudioContext | null = null;
	private inputStream: MediaStream | null = null;
	private inputNode: MediaStreamAudioSourceNode | null = null;
	private analyserNode: AnalyserNode | null = null;
	private gainNode: GainNode | null = null;
	private destinationNode: MediaStreamAudioDestinationNode | null = null;
	private animationFrame: number | null = null;
	
	private devices: AudioDevice[] = [];
	private settings: AudioSettings = {
		inputDeviceId: 'default',
		outputDeviceId: 'default',
		inputGain: 1.0,
		outputGain: 1.0,
		muted: false,
		sampleRate: 48000,
		bufferSize: 256
	};

	private meterCallbacks: ((data: AudioMeterData) => void)[] = [];
	private deviceChangeCallbacks: ((devices: AudioDevice[]) => void)[] = [];

	constructor() {
		// Only initialize in browser environment
		if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
			this.initializeAudioContext();
			this.enumerateDevices();
			
			// Listen for device changes
			if (navigator.mediaDevices) {
				navigator.mediaDevices.addEventListener('devicechange', () => {
					this.enumerateDevices();
				});
			}
		}
	}

	private async initializeAudioContext(): Promise<void> {
		// Only initialize in browser environment
		if (typeof window === 'undefined' || typeof AudioContext === 'undefined') {
			return;
		}

		try {
			this.audioContext = new AudioContext({
				sampleRate: this.settings.sampleRate,
				latencyHint: 'interactive'
			});

			// Resume context if it's suspended
			if (this.audioContext.state === 'suspended') {
				await this.audioContext.resume();
			}
		} catch (error) {
			console.error('Failed to initialize audio context:', error);
		}
	}

	async enumerateDevices(): Promise<AudioDevice[]> {
		// Only work in browser environment
		if (typeof window === 'undefined' || !navigator?.mediaDevices) {
			return [];
		}

		try {
			const devices = await navigator.mediaDevices.enumerateDevices();
			this.devices = devices
				.filter(device => device.kind === 'audioinput' || device.kind === 'audiooutput')
				.map(device => ({
					deviceId: device.deviceId,
					label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
					kind: device.kind as 'audioinput' | 'audiooutput',
					groupId: device.groupId
				}));

			this.deviceChangeCallbacks.forEach(callback => callback(this.devices));
			return this.devices;
		} catch (error) {
			console.error('Failed to enumerate devices:', error);
			return [];
		}
	}

	async requestPermissions(): Promise<boolean> {
		// Only work in browser environment
		if (typeof window === 'undefined' || !navigator?.mediaDevices) {
			return false;
		}

		try {
			const stream = await navigator.mediaDevices.getUserMedia({ 
				audio: true,
				video: false 
			});
			stream.getTracks().forEach(track => track.stop());
			await this.enumerateDevices();
			return true;
		} catch (error) {
			console.error('Failed to request permissions:', error);
			return false;
		}
	}

	async startAudioInput(deviceId?: string): Promise<boolean> {
		// Only work in browser environment
		if (typeof window === 'undefined' || !navigator?.mediaDevices) {
			return false;
		}

		try {
			if (!this.audioContext) {
				await this.initializeAudioContext();
			}

			// Stop existing input
			this.stopAudioInput();

			const constraints: MediaStreamConstraints = {
				audio: {
					deviceId: deviceId ? { exact: deviceId } : undefined,
					echoCancellation: false,
					noiseSuppression: false,
					autoGainControl: false,
					sampleRate: this.settings.sampleRate
				}
			};

			this.inputStream = await navigator.mediaDevices.getUserMedia(constraints);
			
			if (this.audioContext && this.inputStream) {
				this.inputNode = this.audioContext.createMediaStreamSource(this.inputStream);
				this.gainNode = this.audioContext.createGain();
				this.analyserNode = this.audioContext.createAnalyser();
				this.destinationNode = this.audioContext.createMediaStreamDestination();

				// Configure analyser
				this.analyserNode.fftSize = 2048;
				this.analyserNode.smoothingTimeConstant = 0.8;

				// Connect audio graph
				this.inputNode.connect(this.gainNode);
				this.gainNode.connect(this.analyserNode);
				this.analyserNode.connect(this.destinationNode);

				// Set initial gain
				this.gainNode.gain.value = this.settings.inputGain;

				// Start monitoring
				this.startMetering();
			}

			return true;
		} catch (error) {
			console.error('Failed to start audio input:', error);
			return false;
		}
	}

	stopAudioInput(): void {
		if (typeof window !== 'undefined' && this.animationFrame) {
			cancelAnimationFrame(this.animationFrame);
			this.animationFrame = null;
		}

		if (this.inputStream) {
			this.inputStream.getTracks().forEach(track => track.stop());
			this.inputStream = null;
		}

		if (this.inputNode) {
			this.inputNode.disconnect();
			this.inputNode = null;
		}

		if (this.gainNode) {
			this.gainNode.disconnect();
			this.gainNode = null;
		}

		if (this.analyserNode) {
			this.analyserNode.disconnect();
			this.analyserNode = null;
		}

		if (this.destinationNode) {
			this.destinationNode.disconnect();
			this.destinationNode = null;
		}
	}

	private startMetering(): void {
		// Only work in browser environment
		if (typeof window === 'undefined' || !this.analyserNode) {
			return;
		}

		const bufferLength = this.analyserNode.frequencyBinCount;
		const dataArray = new Uint8Array(bufferLength);
		const floatArray = new Float32Array(bufferLength);

		const updateMeter = () => {
			if (!this.analyserNode) return;

			this.analyserNode.getByteFrequencyData(dataArray);
			this.analyserNode.getFloatFrequencyData(floatArray);

			// Calculate RMS and peak
			let sum = 0;
			let peak = 0;
			
			for (let i = 0; i < bufferLength; i++) {
				const value = dataArray[i] / 255;
				sum += value * value;
				peak = Math.max(peak, value);
			}

			const rms = Math.sqrt(sum / bufferLength);
			const level = rms;
			const clipping = peak > 0.95;

			const meterData: AudioMeterData = {
				level,
				peak,
				rms,
				clipping
			};

			this.meterCallbacks.forEach(callback => callback(meterData));
			this.animationFrame = requestAnimationFrame(updateMeter);
		};

		updateMeter();
	}

	setInputGain(gain: number): void {
		this.settings.inputGain = Math.max(0, Math.min(2, gain));
		if (this.gainNode) {
			this.gainNode.gain.value = this.settings.inputGain;
		}
	}

	setOutputGain(gain: number): void {
		this.settings.outputGain = Math.max(0, Math.min(2, gain));
	}

	setMuted(muted: boolean): void {
		this.settings.muted = muted;
		if (this.gainNode) {
			this.gainNode.gain.value = muted ? 0 : this.settings.inputGain;
		}
	}

	getSettings(): AudioSettings {
		return { ...this.settings };
	}

	getDevices(): AudioDevice[] {
		return [...this.devices];
	}

	getInputDevices(): AudioDevice[] {
		return this.devices.filter(device => device.kind === 'audioinput');
	}

	getOutputDevices(): AudioDevice[] {
		return this.devices.filter(device => device.kind === 'audiooutput');
	}

	onMeterUpdate(callback: (data: AudioMeterData) => void): () => void {
		this.meterCallbacks.push(callback);
		return () => {
			const index = this.meterCallbacks.indexOf(callback);
			if (index > -1) {
				this.meterCallbacks.splice(index, 1);
			}
		};
	}

	onDeviceChange(callback: (devices: AudioDevice[]) => void): () => void {
		this.deviceChangeCallbacks.push(callback);
		return () => {
			const index = this.deviceChangeCallbacks.indexOf(callback);
			if (index > -1) {
				this.deviceChangeCallbacks.splice(index, 1);
			}
		};
	}

	async destroy(): Promise<void> {
		this.stopAudioInput();
		
		if (this.audioContext) {
			await this.audioContext.close();
			this.audioContext = null;
		}

		this.meterCallbacks.length = 0;
		this.deviceChangeCallbacks.length = 0;
	}
}

// Lazy initialization to avoid SSR issues
let _audioManager: AudioManager | null = null;

export const audioManager = new Proxy({} as AudioManager, {
	get(target, prop) {
		// Initialize only when first accessed and in browser environment
		if (!_audioManager && typeof window !== 'undefined') {
			_audioManager = new AudioManager();
		}
		
		// Return a no-op function or empty value for SSR
		if (!_audioManager) {
			if (typeof prop === 'string' && prop.includes('on')) {
				return () => () => {}; // Return a function that returns a cleanup function
			}
			return () => Promise.resolve(false); // Return a function that returns a resolved promise
		}
		
		const value = (_audioManager as any)[prop];
		return typeof value === 'function' ? value.bind(_audioManager) : value;
	}
});

