<!-- src/lib/ThemeToggle.svelte -->
<script lang="ts">
	import { Sun, Moon } from '@lucide/svelte';
	import { toggleMode, mode } from 'mode-watcher';
	import { audioManager } from '../audioManager_OLD.js';

	// Make mode reactive with Svelte 5 runes
	let currentMode = $state(mode);
	let count = $state(0);
	let audioStopped = $state(false);
	
	// Simple counter function that doesn't depend on mode-watcher
	const increment = () => {
		count++;
		document.title = `Count: ${count}`;
		
		// Try toggling the mode here
		toggleMode();
		currentMode = mode;
	};
	
	// Function to temporarily stop audio processing
	const toggleAudio = () => {
		if (audioStopped) {
			// Resume audio
			audioManager.startAudioInput();
			audioStopped = false;
		} else {
			// Stop audio
			audioManager.stopAudioInput();
			audioStopped = true;
		}
	};
</script>

<div class="flex flex-col gap-2">
	<button 
		onclick={increment}
		type="button"
		class="border rounded p-2 cursor-pointer"
	>
		Toggle Mode: {currentMode}
	</button>

	<button
		onclick={toggleAudio}
		type="button"
		class="border rounded p-2 cursor-pointer"
	>
		{audioStopped ? 'Resume Audio' : 'Pause Audio'}
	</button>
	
	<div class="text-sm mt-2 p-1 border rounded">
		Count: {count} | Mode: {currentMode} | Audio: {audioStopped ? 'Stopped' : 'Running'}
	</div>
</div>

