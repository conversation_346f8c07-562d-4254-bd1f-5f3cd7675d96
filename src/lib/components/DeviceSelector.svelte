<script lang="ts">
	import { onMount } from 'svelte';
	import { ChevronDown, Mic, Volume2 } from '@lucide/svelte';
	import type { AudioDevice } from '../audioManager_OLD.js';
	import { audioManager } from '../audioManager_OLD.js';

	interface Props {
		type: 'input' | 'output';
		selectedDeviceId?: string;
		onDeviceChange?: (deviceId: string) => void;
	}

	let {
		type,
		selectedDeviceId = 'default',
		onDeviceChange
	}: Props = $props();

	let devices = $state<AudioDevice[]>([]);
	let isOpen = $state(false);
	let unsubscribe: (() => void) | null = null;

	onMount(async () => {
		// Request permissions first
		await audioManager.requestPermissions();
		
		// Get initial devices
		devices = type === 'input' 
			? audioManager.getInputDevices() 
			: audioManager.getOutputDevices();

		// Subscribe to device changes
		unsubscribe = audioManager.onDeviceChange((allDevices) => {
			devices = type === 'input'
				? allDevices.filter(d => d.kind === 'audioinput')
				: allDevices.filter(d => d.kind === 'audiooutput');
		});
	});

	$effect(() => {
		return () => {
			if (unsubscribe) {
				unsubscribe();
			}
		};
	});

	function selectDevice(deviceId: string) {
		selectedDeviceId = deviceId;
		isOpen = false;
		onDeviceChange?.(deviceId);
	}

	function handleClickOutside(event: MouseEvent) {
		const target = event.target as Element;
		if (!target.closest('.device-selector')) {
			isOpen = false;
		}
	}

	$effect(() => {
		if (isOpen) {
			document.addEventListener('click', handleClickOutside);
			return () => document.removeEventListener('click', handleClickOutside);
		}
	});

	let selectedDevice = $derived(devices.find(d => d.deviceId === selectedDeviceId) || {
		deviceId: 'default',
		label: `Default ${type === 'input' ? 'Input' : 'Output'}`,
		kind: type === 'input' ? 'audioinput' as const : 'audiooutput' as const,
		groupId: ''
	});
</script>

<div class="device-selector relative">
	<label class="block text-sm font-medium text-audio-text dark:text-audio-text mb-2">
		{#if type === 'input'}
			<Mic class="inline w-4 h-4 mr-2" />
			Audio Input Device
		{:else}
			<Volume2 class="inline w-4 h-4 mr-2" />
			Audio Output Device
		{/if}
	</label>
	
	<button
		type="button"
		class="w-full flex items-center justify-between px-3 py-2 
		       bg-audio-surface dark:bg-audio-surface 
		       border border-audio-border dark:border-audio-border 
		       text-audio-text dark:text-audio-text
		       rounded-lg text-left 
		       hover:bg-audio-border dark:hover:bg-audio-border 
		       transition-colors"
		onclick={() => isOpen = !isOpen}
	>
		<span class="truncate">{selectedDevice.label}</span>
		<ChevronDown class="w-4 h-4 transition-transform {isOpen ? 'rotate-180' : ''}" />
	</button>
	
	{#if isOpen}
		<div class="absolute z-10 mt-1 w-full 
		            bg-audio-surface dark:bg-audio-surface 
		            border border-audio-border dark:border-audio-border 
		            rounded-lg shadow-lg max-h-60 overflow-auto">
			{#if devices.length === 0}
				<div class="px-3 py-2 text-audio-muted dark:text-audio-muted text-sm">
					No {type === 'input' ? 'input' : 'output'} devices found
				</div>
			{:else}
				{#each devices as device (device.deviceId)}
					<button
						type="button"
						class="w-full px-3 py-2 text-left 
						       hover:bg-audio-border dark:hover:bg-audio-border 
						       transition-colors border-b border-audio-border dark:border-audio-border 
						       last:border-b-0 
						       {device.deviceId === selectedDeviceId 
						       	? 'bg-audio-accent/20 dark:bg-audio-accent/20 text-audio-accent dark:text-audio-accent' 
						       	: 'text-audio-text dark:text-audio-text'}"
						onclick={() => selectDevice(device.deviceId)}
					>
						<div class="truncate">{device.label}</div>
						{#if device.deviceId === 'default'}
							<div class="text-xs text-audio-muted dark:text-audio-muted">System Default</div>
						{/if}
					</button>
				{/each}
			{/if}
		</div>
	{/if}
</div>
