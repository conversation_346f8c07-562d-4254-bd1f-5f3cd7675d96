<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { audioManager } from '../audioManager_OLD.js';

	interface Props {
		width?: number;
		height?: number;
		barCount?: number;
		smoothing?: number;
		logScale?: boolean;
	}

	let {
		width = 400,
		height = 200,
		barCount = 64,
		smoothing = 0.8,
		logScale = true
	}: Props = $props();

	let canvas: HTMLCanvasElement;
	let ctx: CanvasRenderingContext2D;
	let animationFrame: number | null = null;
	let isActive = $state(false);

	onMount(() => {
		ctx = canvas.getContext('2d')!;
		startAnalysis();
	});

	$effect(() => {
		return () => {
			stopAnalysis();
		};
	});

	function startAnalysis() {
		if (!audioManager || !ctx) return;
		
		isActive = true;
		draw();
	}

	function stopAnalysis() {
		isActive = false;
		if (animationFrame) {
			cancelAnimationFrame(animationFrame);
			animationFrame = null;
		}
	}

	function draw() {
		if (!isActive || !ctx) return;

		// Get audio context and analyser from the audio manager
		const audioContext = (audioManager as any).audioContext;
		const analyser = (audioManager as any).analyserNode;

		if (!audioContext || !analyser) {
			// Clear canvas if no audio
			ctx.fillStyle = '#0f0f0f';
			ctx.fillRect(0, 0, width, height);
			animationFrame = requestAnimationFrame(draw);
			return;
		}

		// Get frequency data
		const bufferLength = analyser.frequencyBinCount;
		const dataArray = new Uint8Array(bufferLength);
		analyser.getByteFrequencyData(dataArray);

		// Clear canvas
		ctx.fillStyle = '#0f0f0f';
		ctx.fillRect(0, 0, width, height);

		// Calculate bar width
		const barWidth = width / barCount;
		const nyquist = audioContext.sampleRate / 2;

		// Draw frequency bars
		for (let i = 0; i < barCount; i++) {
			let dataIndex: number;
			
			if (logScale) {
				// Logarithmic scale for more natural frequency representation
				const minFreq = 20; // 20 Hz
				const maxFreq = nyquist; // Nyquist frequency
				const logMin = Math.log10(minFreq);
				const logMax = Math.log10(maxFreq);
				const logFreq = logMin + (i / (barCount - 1)) * (logMax - logMin);
				const freq = Math.pow(10, logFreq);
				dataIndex = Math.floor((freq / nyquist) * bufferLength);
			} else {
				// Linear scale
				dataIndex = Math.floor((i / barCount) * bufferLength);
			}

			// Ensure we don't exceed array bounds
			dataIndex = Math.min(dataIndex, bufferLength - 1);
			
			const amplitude = dataArray[dataIndex];
			const barHeight = (amplitude / 255) * height;

			// Calculate color based on amplitude and frequency
			const hue = (i / barCount) * 240; // Blue to red spectrum
			const saturation = 70 + (amplitude / 255) * 30; // More saturated for higher amplitude
			const lightness = 30 + (amplitude / 255) * 50; // Brighter for higher amplitude
			
			ctx.fillStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
			
			// Draw bar from bottom up
			const x = i * barWidth;
			const y = height - barHeight;
			
			ctx.fillRect(x, y, barWidth - 1, barHeight);

			// Add gradient effect
			if (barHeight > 0) {
				const gradient = ctx.createLinearGradient(0, y, 0, height);
				gradient.addColorStop(0, `hsl(${hue}, ${saturation}%, ${lightness + 20}%)`);
				gradient.addColorStop(1, `hsl(${hue}, ${saturation}%, ${lightness}%)`);
				ctx.fillStyle = gradient;
				ctx.fillRect(x, y, barWidth - 1, barHeight);
			}
		}

		// Draw frequency labels
		ctx.fillStyle = '#888888';
		ctx.font = '10px monospace';
		ctx.textAlign = 'center';

		// Draw some frequency markers
		const freqMarkers = logScale 
			? [20, 100, 500, 1000, 5000, 10000, 20000]
			: [0, 2000, 4000, 8000, 12000, 16000, 20000];

		freqMarkers.forEach(freq => {
			if (freq > nyquist) return;
			
			let x: number;
			if (logScale) {
				const logMin = Math.log10(20);
				const logMax = Math.log10(nyquist);
				const logFreq = Math.log10(freq);
				x = ((logFreq - logMin) / (logMax - logMin)) * width;
			} else {
				x = (freq / nyquist) * width;
			}

			if (x >= 0 && x <= width) {
				ctx.fillText(freq >= 1000 ? `${freq/1000}k` : `${freq}`, x, height - 5);
				
				// Draw frequency line
				ctx.beginPath();
				ctx.moveTo(x, 0);
				ctx.lineTo(x, height - 15);
				ctx.strokeStyle = '#333333';
				ctx.lineWidth = 0.5;
				ctx.stroke();
			}
		});

		animationFrame = requestAnimationFrame(draw);
	}

	// Handle canvas resize
	$effect(() => {
		if (canvas) {
			canvas.width = width;
			canvas.height = height;
		}
	});
</script>

<div class="flex flex-col gap-2">
	<div class="flex items-center justify-between">
		<h3 class="text-sm font-medium text-audio-text light:text-audio-text-light">Spectrum Analyzer</h3>
		<div class="flex items-center gap-2 text-xs text-audio-muted light:text-audio-muted-light">
			<span>{logScale ? 'Log' : 'Linear'}</span>
			<span>•</span>
			<span>{barCount} bars</span>
		</div>
	</div>
	
	<div class="bg-audio-surface light:bg-audio-surface-light 
	            border border-audio-border light:border-audio-border-light 
	            rounded-lg p-2 transition-colors">
		<canvas
			bind:this={canvas}
			{width}
			{height}
			class="w-full h-auto rounded"
			style="max-width: 100%; height: auto;"
		></canvas>
	</div>
</div>
